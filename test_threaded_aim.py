#!/usr/bin/env python3
"""
Test script to verify the threaded aim system works correctly
"""

import time
import threading
import serial
from main_optmized import AimAssistThread, SilentAimThread, AimLockThread

def test_threaded_aim_systems():
    """Test the threaded aim systems"""
    print("Testing threaded aim systems...")
    
    # Mock serial communication for testing
    class MockSerial:
        def write(self, data):
            print(f"Mock serial write: {data.decode()}")
    
    mock_serial = MockSerial()
    
    # Create threaded aim systems
    aim_assist_thread = AimAssistThread(mock_serial)
    silent_aim_thread = SilentAimThread(mock_serial)
    aim_lock_thread = AimLockThread(mock_serial)
    
    # Start all threads
    aim_assist_thread.start()
    silent_aim_thread.start()
    aim_lock_thread.start()
    
    print("All threads started successfully!")
    
    # Test data
    test_data = {
        'stable_target': [100, 100],
        'closest_part_distance': 50,
        'closest_detection': {'xmin': 90, 'ymin': 90, 'xmax': 110, 'ymax': 110},
        'current_fps': 100,
        'screenshot_center': [192, 108],
        'mouse_button_0x05_pressed': False,
        'mouse_button_0x06_pressed': False,
        'ctrl_key_pressed': False,
        'target_locked': True
    }
    
    # Test aim assist
    print("\n--- Testing Aim Assist ---")
    aim_assist_thread.set_enabled(True)
    aim_assist_thread.update_shared_data(**test_data)
    time.sleep(0.1)  # Let it process
    
    # Test silent aim
    print("\n--- Testing Silent Aim ---")
    silent_aim_thread.set_enabled(True)
    test_data['mouse_button_0x06_pressed'] = True
    silent_aim_thread.update_shared_data(**test_data)
    time.sleep(0.1)  # Let it process
    
    # Test aim lock
    print("\n--- Testing Aim Lock ---")
    aim_lock_thread.set_enabled(True)
    test_data['mouse_button_0x05_pressed'] = True
    test_data['mouse_button_0x06_pressed'] = False  # Reset silent aim button
    aim_lock_thread.update_shared_data(**test_data)
    time.sleep(0.1)  # Let it process
    
    # Stop all threads
    print("\n--- Stopping all threads ---")
    aim_assist_thread.stop()
    silent_aim_thread.stop()
    aim_lock_thread.stop()
    
    print("Test completed successfully!")

if __name__ == "__main__":
    test_threaded_aim_systems()
